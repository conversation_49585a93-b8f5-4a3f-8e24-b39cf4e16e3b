'use client';

import { DamageComplaintForm } from '@/features/complaints/components/damage-complaint-form';
import { useRouter } from 'next/navigation';

export default function NewComplaintPage() {
  const router = useRouter();

  const handleSuccess = () => {
    // Redirect back to complaints list with success message
    router.push('/complaints?success=created');
  };

  const handleCancel = () => {
    // Go back to complaints list
    router.back();
  };

  return (
    <DamageComplaintForm
      onSuccess={handleSuccess}
      onCancel={handleCancel}
      editMode={false}
    />
  );
}
